#!/usr/bin/env python3
"""
Enhanced Fake User Manager for Genesis Telegram Bot

This tool provides comprehensive fake user management capabilities:
- Insert realistic fake users with proper referral relationships
- Create corresponding REFERRAL_BONUS transactions
- Update referrer balances automatically
- Maintain database consistency across all collections
- Reusable for future testing and demonstration purposes

Usage:
    python tools/fake_user_manager.py --action insert --count 10 --referrer-id 7463061717
    python tools/fake_user_manager.py --action list --referrer-id 7463061717
    python tools/fake_user_manager.py --action cleanup --referrer-id 7463061717

Features:
- Realistic user data generation with diverse backgrounds
- Proper transaction creation for referral bonuses
- Automatic balance updates for referrers
- Database consistency validation
- Comprehensive error handling and logging
"""

import asyncio
import argparse
import logging
import random
import sys
import os
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional

# Add the parent directory to the path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database import Database
from src.models.user import User
from src.models.referral import Referral, ReferralStatus
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FakeUserManager:
    """Enhanced fake user management with transaction support"""
    
    def __init__(self):
        self.db = Database()
        
        # Realistic data pools for diverse fake users
        self.first_names = [
            "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
            "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
            "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa",
            "Matthew", "Betty", "Anthony", "Helen", "Mark", "Sandra", "Donald", "Donna",
            "Steven", "Carol", "Paul", "Ruth", "Andrew", "Sharon", "Joshua", "Michelle",
            "Kenneth", "Laura", "Kevin", "Sarah", "Brian", "Kimberly", "George", "Deborah",
            "Edward", "Dorothy", "Ronald", "Lisa", "Timothy", "Nancy", "Jason", "Karen",
            "Jeffrey", "Betty", "Ryan", "Helen", "Jacob", "Sandra", "Gary", "Donna",
            "Nicholas", "Carol", "Eric", "Ruth", "Jonathan", "Sharon", "Stephen", "Michelle",
            "Larry", "Laura", "Justin", "Sarah", "Scott", "Kimberly", "Brandon", "Deborah",
            "Benjamin", "Dorothy", "Samuel", "Amy", "Gregory", "Angela", "Alexander", "Ashley",
            "Frank", "Brenda", "Raymond", "Emma", "Jack", "Olivia", "Dennis", "Cynthia",
            "Jerry", "Marie", "Tyler", "Janet", "Aaron", "Catherine", "Jose", "Frances",
            "Henry", "Christine", "Adam", "Samantha", "Douglas", "Debra", "Nathan", "Rachel",
            "Peter", "Carolyn", "Zachary", "Janet", "Kyle", "Virginia", "Noah", "Maria",
            "Alan", "Heather", "Carl", "Diane", "Wayne", "Julie", "Arthur", "Joyce",
            "Gerald", "Victoria", "Harold", "Kelly", "Jordan", "Christina", "Jesse", "Joan",
            "Bryan", "Evelyn", "Lawrence", "Lauren", "Arthur", "Judith", "Gabriel", "Megan",
            "Bruce", "Cheryl", "Logan", "Andrea", "Billy", "Hannah", "Joe", "Jacqueline",
            "Willie", "Martha", "Ralph", "Gloria", "Roy", "Teresa", "Eugene", "Sara",
            "Louis", "Janice", "Philip", "Marie", "Bobby", "Julia", "Johnny", "Heather",
            "Mason", "Diane", "Wayne", "Ruth", "Roy", "Julie", "Eugene", "Joyce",
            "Louis", "Virginia", "Philip", "Victoria", "Bobby", "Kelly", "Johnny", "Christina",
            # International names for diversity
            "Ahmed", "Fatima", "Mohammed", "Aisha", "Ali", "Zara", "Omar", "Layla",
            "Hassan", "Amina", "Yusuf", "Khadija", "Ibrahim", "Maryam", "Khalid", "Nour",
            "Carlos", "Maria", "Jose", "Ana", "Luis", "Carmen", "Miguel", "Rosa",
            "Antonio", "Elena", "Francisco", "Isabel", "Manuel", "Lucia", "Rafael", "Sofia",
            "Wei", "Li", "Zhang", "Wang", "Liu", "Chen", "Yang", "Huang",
            "Zhao", "Wu", "Zhou", "Xu", "Sun", "Ma", "Zhu", "Hu",
            "Hiroshi", "Yuki", "Takeshi", "Akiko", "Kenji", "Naomi", "Satoshi", "Emi",
            "Dmitri", "Natasha", "Vladimir", "Olga", "Sergei", "Irina", "Alexei", "Svetlana",
            "Giovanni", "Francesca", "Marco", "Giulia", "Andrea", "Chiara", "Luca", "Valentina",
            "Pierre", "Marie", "Jean", "Sophie", "Michel", "Camille", "Antoine", "Chloe"
        ]
        
        self.last_names = [
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
            "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
            "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
            "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young",
            "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
            "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
            "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
            "Cruz", "Edwards", "Collins", "Reyes", "Stewart", "Morris", "Morales", "Murphy",
            "Cook", "Rogers", "Gutierrez", "Ortiz", "Morgan", "Cooper", "Peterson", "Bailey",
            "Reed", "Kelly", "Howard", "Ramos", "Kim", "Cox", "Ward", "Richardson",
            "Watson", "Brooks", "Chavez", "Wood", "James", "Bennett", "Gray", "Mendoza",
            "Ruiz", "Hughes", "Price", "Alvarez", "Castillo", "Sanders", "Patel", "Myers",
            "Long", "Ross", "Foster", "Jimenez", "Powell", "Jenkins", "Perry", "Russell",
            "Sullivan", "Bell", "Coleman", "Butler", "Henderson", "Barnes", "Gonzales", "Fisher",
            "Vasquez", "Simmons", "Romero", "Jordan", "Patterson", "Alexander", "Hamilton", "Graham",
            "Reynolds", "Griffin", "Wallace", "Moreno", "West", "Cole", "Hayes", "Bryant",
            "Herrera", "Gibson", "Ellis", "Tran", "Medina", "Aguilar", "Stevens", "Murray",
            "Ford", "Castro", "Marshall", "Owens", "Harrison", "Fernandez", "McDonald", "Woods",
            "Washington", "Kennedy", "Wells", "Vargas", "Henry", "Chen", "Freeman", "Webb",
            "Tucker", "Guzman", "Burns", "Crawford", "Olson", "Simpson", "Porter", "Hunter",
            "Gordon", "Mendez", "Silva", "Shaw", "Snyder", "Mason", "Dixon", "Munoz",
            "Hunt", "Hicks", "Holmes", "Palmer", "Wagner", "Black", "Robertson", "Boyd",
            "Rose", "Stone", "Salazar", "Fox", "Warren", "Mills", "Meyer", "Rice",
            "Schmidt", "Garza", "Daniels", "Ferguson", "Nichols", "Stephens", "Soto", "Weaver",
            "Ryan", "Gardner", "Payne", "Grant", "Dunn", "Kelley", "Spencer", "Hawkins",
            "Arnold", "Pierce", "Vazquez", "Hansen", "Peters", "Santos", "Hart", "Bradley",
            "Knight", "Elliott", "Cunningham", "Duncan", "Armstrong", "Hudson", "Carroll", "Lane",
            "Riley", "Andrews", "Alvarado", "Ray", "Delgado", "Berry", "Perkins", "Hoffman",
            "Johnston", "Matthews", "Pena", "Richards", "Contreras", "Willis", "Carpenter", "Lawrence",
            "Sandoval", "Guerrero", "George", "Chapman", "Rios", "Estrada", "Ortega", "Watkins",
            "Greene", "Nunez", "Wheeler", "Valdez", "Harper", "Burke", "Larson", "Santiago",
            "Maldonado", "Morrison", "Franklin", "Carlson", "Austin", "Dominguez", "Carr", "Lawson",
            "Jacobs", "Obrien", "Lynch", "Singh", "Vega", "Bishop", "Montgomery", "Oliver",
            "Jensen", "Harvey", "Williamson", "Gilbert", "Dean", "Sims", "Espinoza", "Howell",
            "Li", "Wong", "Lam", "Chan", "Leung", "Ng", "Ma", "Tang",
            "Cheng", "Yuen", "Ho", "Lau", "Cheung", "Tsang", "Kwok", "Hui"
        ]
        
        self.username_patterns = [
            "{first_name}_{last_name}",
            "{first_name}.{last_name}",
            "{first_name}{last_name}",
            "{first_name}_{random_num}",
            "{last_name}_{first_name}",
            "{first_name}{random_num}",
            "{last_name}.{first_name}",
            "{first_name}_{last_name}_{random_num}"
        ]
    
    async def connect(self):
        """Connect to database"""
        await self.db.connect()
    
    async def disconnect(self):
        """Disconnect from database"""
        await self.db.disconnect()
    
    def generate_realistic_user_id(self) -> int:
        """Generate realistic Telegram user ID (8-10 digits)"""
        return random.randint(10000000, 9999999999)
    
    def generate_username(self, first_name: str, last_name: str) -> Optional[str]:
        """Generate realistic username or None (some users don't have usernames)"""
        # 20% chance of no username (realistic for Telegram)
        if random.random() < 0.2:
            return None
        
        pattern = random.choice(self.username_patterns)
        random_num = random.randint(10, 99)
        
        username = pattern.format(
            first_name=first_name.lower(),
            last_name=last_name.lower(),
            random_num=random_num
        )
        
        # Ensure username meets Telegram requirements (5-32 chars, alphanumeric + underscore)
        username = ''.join(c for c in username if c.isalnum() or c == '_')
        if len(username) < 5:
            username += str(random.randint(100, 999))
        elif len(username) > 32:
            username = username[:32]
        
        return username

    async def get_referrer_info(self, referrer_id: int) -> Optional[Dict[str, Any]]:
        """Get referrer information and validate"""
        try:
            user_data = await self.db.db.users.find_one({'user_id': referrer_id})
            if not user_data:
                logger.error(f"Referrer user {referrer_id} not found")
                return None

            # Get current referral counts
            total_referrals = await self.db.db.referrals.count_documents({'referrer_id': referrer_id})
            successful_referrals = await self.db.db.referrals.count_documents({
                'referrer_id': referrer_id,
                'status': ReferralStatus.COMPLETED.value
            })

            return {
                'user': User.from_dict(user_data),
                'total_referrals': total_referrals,
                'successful_referrals': successful_referrals,
                'referral_code': user_data.get('referral_code')
            }

        except Exception as e:
            logger.error(f"Failed to get referrer info for {referrer_id}: {e}")
            return None

    async def create_referral_transaction(self, referrer_id: int, referred_id: int, amount: float) -> Optional[str]:
        """Create a REFERRAL_BONUS transaction for the referrer"""
        try:
            transaction = Transaction(
                user_id=referrer_id,
                amount=amount,
                transaction_type=TransactionType.REFERRAL_BONUS,
                status=TransactionStatus.COMPLETED,
                description=f"Referral bonus for user {referred_id}",
                metadata={
                    'referred_user_id': referred_id,
                    'referral_reward': amount,
                    'created_by': 'fake_user_manager'
                }
            )

            # Insert transaction
            await self.db.db.transactions.insert_one(transaction.to_dict())
            logger.info(f"Created referral transaction: {transaction.transaction_id} for user {referrer_id}")

            return transaction.transaction_id

        except Exception as e:
            logger.error(f"Failed to create referral transaction for {referrer_id}: {e}")
            return None

    async def update_referrer_balance(self, referrer_id: int, amount: float) -> bool:
        """Update referrer's balance and successful_referrals count"""
        try:
            result = await self.db.db.users.update_one(
                {'user_id': referrer_id},
                {
                    '$inc': {
                        'balance': amount,
                        'successful_referrals': 1
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"Updated referrer {referrer_id} balance by +{amount} tokens")
                return True
            else:
                logger.error(f"Failed to update referrer {referrer_id} balance")
                return False

        except Exception as e:
            logger.error(f"Failed to update referrer balance for {referrer_id}: {e}")
            return False

    async def create_fake_users(self, count: int, referrer_id: int) -> Dict[str, Any]:
        """Create multiple fake users with complete referral setup"""
        try:
            # Validate referrer
            referrer_info = await self.get_referrer_info(referrer_id)
            if not referrer_info:
                return {'success': False, 'error': 'Invalid referrer ID'}

            referrer = referrer_info['user']
            referral_code = referrer_info['referral_code']

            logger.info(f"Creating {count} fake users referred by {referrer.get_display_name()} (ID: {referrer_id})")

            created_users = []
            created_referrals = []
            created_transactions = []
            total_bonus_amount = 0

            for i in range(count):
                try:
                    # Generate unique user ID
                    user_id = self.generate_realistic_user_id()
                    while await self.db.db.users.find_one({'user_id': user_id}):
                        user_id = self.generate_realistic_user_id()

                    # Generate realistic user data
                    first_name = random.choice(self.first_names)
                    last_name = random.choice(self.last_names)
                    username = self.generate_username(first_name, last_name)

                    # Create join timestamp (recent but varied)
                    join_time = datetime.now(timezone.utc) - timedelta(
                        hours=random.randint(1, 72),
                        minutes=random.randint(0, 59)
                    )

                    # Create user with joining bonus
                    user = User(
                        user_id=user_id,
                        username=username,
                        first_name=first_name,
                        last_name=last_name,
                        language_code='en',
                        is_premium=random.choice([True, False]),
                        balance=25.0,  # Standard joining bonus
                        successful_referrals=0,
                        is_active=True,
                        is_banned=False,
                        created_at=join_time,
                        referred_by=referrer_id
                    )

                    # Insert user
                    await self.db.db.users.insert_one(user.to_dict())
                    created_users.append(user)

                    # Create referral with COMPLETED status
                    referral = Referral(
                        referrer_id=referrer_id,
                        referred_id=user_id,
                        referral_code=referral_code,
                        reward_amount=Config.REFERRAL_REWARD,
                        status=ReferralStatus.COMPLETED,
                        is_rewarded=True
                    )

                    # Set completion timestamp slightly after user creation
                    referral.completed_at = join_time + timedelta(minutes=random.randint(1, 10))
                    referral.rewarded_at = referral.completed_at

                    # Insert referral
                    await self.db.db.referrals.insert_one(referral.to_dict())
                    created_referrals.append(referral)

                    # Create referral bonus transaction for referrer
                    transaction_id = await self.create_referral_transaction(
                        referrer_id, user_id, Config.REFERRAL_REWARD
                    )
                    if transaction_id:
                        created_transactions.append(transaction_id)
                        total_bonus_amount += Config.REFERRAL_REWARD

                    logger.info(f"Created fake user {i+1}/{count}: {user.get_display_name()} (ID: {user_id})")

                except Exception as e:
                    logger.error(f"Failed to create fake user {i+1}: {e}")
                    continue

            # Update referrer's balance and successful_referrals count
            if total_bonus_amount > 0:
                await self.update_referrer_balance(referrer_id, total_bonus_amount)

            # Get updated referrer info
            updated_referrer_info = await self.get_referrer_info(referrer_id)

            result = {
                'success': True,
                'created_users': len(created_users),
                'created_referrals': len(created_referrals),
                'created_transactions': len(created_transactions),
                'total_bonus_amount': total_bonus_amount,
                'referrer_info': {
                    'before': {
                        'total_referrals': referrer_info['total_referrals'],
                        'successful_referrals': referrer_info['successful_referrals'],
                        'balance': referrer.balance
                    },
                    'after': {
                        'total_referrals': updated_referrer_info['total_referrals'],
                        'successful_referrals': updated_referrer_info['successful_referrals'],
                        'balance': updated_referrer_info['user'].balance
                    }
                },
                'user_ids': [user.user_id for user in created_users]
            }

            logger.info(f"Successfully created {len(created_users)} fake users with complete referral setup")
            return result

        except Exception as e:
            logger.error(f"Failed to create fake users: {e}")
            return {'success': False, 'error': str(e)}

    async def list_referrals(self, referrer_id: int, limit: int = 20) -> Dict[str, Any]:
        """List referrals for a specific referrer"""
        try:
            referrer_info = await self.get_referrer_info(referrer_id)
            if not referrer_info:
                return {'success': False, 'error': 'Invalid referrer ID'}

            # Get recent referrals with user details
            pipeline = [
                {"$match": {"referrer_id": referrer_id}},
                {"$sort": {"created_at": -1}},
                {"$limit": limit},
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "referred_id",
                        "foreignField": "user_id",
                        "as": "user_details"
                    }
                },
                {"$unwind": {"path": "$user_details", "preserveNullAndEmptyArrays": True}}
            ]

            referrals = []
            async for referral_data in self.db.db.referrals.aggregate(pipeline):
                user_details = referral_data.get('user_details', {})
                referrals.append({
                    'referral_id': referral_data.get('referral_id'),
                    'referred_id': referral_data.get('referred_id'),
                    'status': referral_data.get('status'),
                    'reward_amount': referral_data.get('reward_amount'),
                    'created_at': referral_data.get('created_at'),
                    'completed_at': referral_data.get('completed_at'),
                    'user_name': user_details.get('first_name', 'Unknown'),
                    'username': user_details.get('username'),
                    'user_balance': user_details.get('balance', 0)
                })

            return {
                'success': True,
                'referrer_info': referrer_info,
                'referrals': referrals,
                'total_shown': len(referrals)
            }

        except Exception as e:
            logger.error(f"Failed to list referrals for {referrer_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def cleanup_fake_users(self, referrer_id: int, confirm: bool = False) -> Dict[str, Any]:
        """Remove fake users created by this tool (use with caution!)"""
        if not confirm:
            return {
                'success': False,
                'error': 'Cleanup requires explicit confirmation. Use --confirm flag.'
            }

        try:
            # Find fake users (those with metadata indicating they were created by this tool)
            fake_users = []
            async for transaction in self.db.db.transactions.find({
                'user_id': referrer_id,
                'transaction_type': TransactionType.REFERRAL_BONUS.value,
                'metadata.created_by': 'fake_user_manager'
            }):
                referred_id = transaction.get('metadata', {}).get('referred_user_id')
                if referred_id:
                    fake_users.append(referred_id)

            if not fake_users:
                return {'success': True, 'message': 'No fake users found to cleanup'}

            # Remove fake users, referrals, and transactions
            deleted_users = await self.db.db.users.delete_many({'user_id': {'$in': fake_users}})
            deleted_referrals = await self.db.db.referrals.delete_many({'referred_id': {'$in': fake_users}})
            deleted_transactions = await self.db.db.transactions.delete_many({
                'metadata.created_by': 'fake_user_manager'
            })

            # Recalculate referrer's balance and successful_referrals
            # This is a simplified approach - in production, you'd want more sophisticated balance recalculation
            total_removed_bonus = len(fake_users) * Config.REFERRAL_REWARD
            await self.db.db.users.update_one(
                {'user_id': referrer_id},
                {
                    '$inc': {
                        'balance': -total_removed_bonus,
                        'successful_referrals': -len(fake_users)
                    }
                }
            )

            return {
                'success': True,
                'deleted_users': deleted_users.deleted_count,
                'deleted_referrals': deleted_referrals.deleted_count,
                'deleted_transactions': deleted_transactions.deleted_count,
                'removed_bonus': total_removed_bonus,
                'fake_user_ids': fake_users
            }

        except Exception as e:
            logger.error(f"Failed to cleanup fake users: {e}")
            return {'success': False, 'error': str(e)}


async def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(description='Enhanced Fake User Manager for Genesis Telegram Bot')
    parser.add_argument('--action', choices=['insert', 'list', 'cleanup'], required=True,
                       help='Action to perform')
    parser.add_argument('--count', type=int, default=10,
                       help='Number of fake users to create (for insert action)')
    parser.add_argument('--referrer-id', type=int, required=True,
                       help='Referrer user ID')
    parser.add_argument('--limit', type=int, default=20,
                       help='Limit for list action')
    parser.add_argument('--confirm', action='store_true',
                       help='Confirm cleanup action (required for cleanup)')

    args = parser.parse_args()

    manager = FakeUserManager()

    try:
        await manager.connect()

        if args.action == 'insert':
            result = await manager.create_fake_users(args.count, args.referrer_id)
            if result['success']:
                print(f"✅ Successfully created {result['created_users']} fake users")
                print(f"📊 Created {result['created_referrals']} referrals")
                print(f"💰 Created {result['created_transactions']} transactions")
                print(f"🎯 Total bonus amount: {result['total_bonus_amount']} tokens")
                print(f"👥 User IDs: {result['user_ids']}")

                before = result['referrer_info']['before']
                after = result['referrer_info']['after']
                print(f"\n📈 Referrer Updates:")
                print(f"   Total Referrals: {before['total_referrals']} → {after['total_referrals']}")
                print(f"   Successful Referrals: {before['successful_referrals']} → {after['successful_referrals']}")
                print(f"   Balance: {before['balance']} → {after['balance']} tokens")
            else:
                print(f"❌ Failed to create fake users: {result['error']}")

        elif args.action == 'list':
            result = await manager.list_referrals(args.referrer_id, args.limit)
            if result['success']:
                referrer_info = result['referrer_info']
                print(f"📋 Referrals for {referrer_info['user'].get_display_name()} (ID: {args.referrer_id})")
                print(f"📊 Total: {referrer_info['total_referrals']}, Successful: {referrer_info['successful_referrals']}")
                print(f"💰 Current Balance: {referrer_info['user'].balance} tokens")
                print(f"\n🎯 Recent {result['total_shown']} referrals:")

                for i, ref in enumerate(result['referrals'], 1):
                    # Handle both uppercase and lowercase status values
                    status_emoji = "✅" if ref['status'].lower() == 'completed' else "⏳"
                    username_display = f" (@{ref['username']})" if ref['username'] else ""
                    print(f"   {i}. {status_emoji} {ref['user_name']}{username_display} - ID: {ref['referred_id']}")
                    print(f"      💰 {ref['user_balance']} tokens | 🎁 {ref['reward_amount']} reward")
            else:
                print(f"❌ Failed to list referrals: {result['error']}")

        elif args.action == 'cleanup':
            result = await manager.cleanup_fake_users(args.referrer_id, args.confirm)
            if result['success']:
                if 'message' in result:
                    print(f"ℹ️ {result['message']}")
                else:
                    print(f"🧹 Cleanup completed:")
                    print(f"   Deleted {result['deleted_users']} users")
                    print(f"   Deleted {result['deleted_referrals']} referrals")
                    print(f"   Deleted {result['deleted_transactions']} transactions")
                    print(f"   Removed {result['removed_bonus']} bonus tokens")
                    print(f"   Fake user IDs: {result['fake_user_ids']}")
            else:
                print(f"❌ Cleanup failed: {result['error']}")

    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"❌ Application error: {e}")

    finally:
        await manager.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
