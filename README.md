# Genesis Telegram Referral Bot 🚀

A comprehensive Telegram bot for managing referral rewards and community engagement using the Genesis Token system. Built with Python and MongoDB, featuring a robust referral system, leaderboards, and automated reward distribution.

## 🌟 Features

### Core Functionality
- **Referral System**: Users earn Genesis Tokens by inviting friends
- **Automated Rewards**: 50 tokens per successful referral, 25 tokens welcome bonus for new users
- **Leaderboard**: Top 5 users tracking with weekly prizes
- **Balance Management**: Real-time token balance tracking
- **Transaction History**: Complete audit trail of all token transactions

### Bot Commands & Menus
- **🚀 Earn Genesis Token**: Get referral link and track referral stats
- **💰 Genesis Token Balance**: Check current balance and transaction history
- **🏆 Top 5 Users**: View leaderboard rankings
- **ℹ️ About Genesis Bot**: Learn about the reward system
- **❓ How to Earn Genesis Token**: Step-by-step earning guide

### Technical Features
- **MongoDB Integration**: Scalable database with transaction support
- **Async Architecture**: High-performance async/await implementation
- **Error Handling**: Comprehensive logging and error recovery
- **Admin Panel**: Administrative controls and user management
- **Security**: Input validation and rate limiting

## 🛠️ Technology Stack

- **Python 3.12+**: Core programming language
- **python-telegram-bot 22.3**: Telegram Bot API wrapper
- **MongoDB**: Database with Motor async driver
- **AsyncIO**: Asynchronous programming support

## 📋 Prerequisites

- Python 3.12 or higher
- MongoDB Atlas account or local MongoDB instance
- Telegram Bot Token (from @BotFather)
- Environment variables configured

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd genesis
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
Create a `.env` file in the root directory:

```env
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token
BOT_USERNAME=your_bot_username

# Database Configuration
MONGODB_URI=your_mongodb_connection_string
DATABASE_NAME=referral_bot_db

# Admin Configuration
ADMIN_USER_IDS=*********,*********
ADMIN_PASSWORD=your_admin_password

# Reward Configuration
REFERRAL_REWARD=50
FRIEND_WELCOME_BONUS=25

# Channel Configuration (optional)
REQUIRED_CHANNELS=@your_channel1,@your_channel2
```

### 4. Run the Bot
```bash
python final_bot.py
```

## 📁 Project Structure

```
genesis/
├── final_bot.py              # Main bot application
├── config.py                 # Configuration management
├── requirements.txt          # Python dependencies
├── src/
│   ├── __init__.py
│   ├── database.py           # MongoDB connection and operations
│   ├── models/               # Data models
│   │   ├── __init__.py
│   │   ├── user.py          # User model
│   │   ├── referral.py      # Referral model
│   │   └── transaction.py   # Transaction model
│   ├── services/            # Business logic services
│   │   ├── user_service.py  # User management
│   │   └── referral_service.py # Referral processing
│   └── utils/               # Utility functions
├── logs/                    # Application logs
│   ├── bot.log
│   ├── error.log
│   ├── security.log
│   └── transactions.log
└── assets/                  # Bot images
    ├── start.jpg
    └── join.jpg
```

## 🎯 How It Works

### Referral Process
1. **User Registration**: New users join via referral links or directly
2. **Welcome Bonus**: New users receive 25 Genesis Tokens upon joining
3. **Referral Rewards**: Referrers earn 50 Genesis Tokens per successful referral
4. **Validation**: Referrals are validated through channel membership (optional)
5. **Leaderboard**: Users compete for top positions and weekly prizes

### Token Economy
- **Referral Reward**: 50 Genesis Tokens per successful referral
- **Welcome Bonus**: 25 Genesis Tokens for new users
- **Leaderboard Prizes**: Weekly rewards for top 5 users
- **Community Giveaways**: Regular prizes for active participants

## 🔧 Configuration Options

### Environment Variables
- `BOT_TOKEN`: Your Telegram bot token
- `BOT_USERNAME`: Your bot's username
- `MONGODB_URI`: MongoDB connection string
- `DATABASE_NAME`: Database name (default: referral_bot_db)
- `ADMIN_USER_IDS`: Comma-separated admin user IDs
- `REFERRAL_REWARD`: Tokens earned per referral (default: 50)
- `FRIEND_WELCOME_BONUS`: Welcome bonus for new users (default: 25)

### Optional Features
- Channel membership validation
- Custom reward amounts
- Admin controls and monitoring
- Transaction logging and auditing

## 📊 Database Schema

### Users Collection
- User profile information
- Token balance and referral stats
- Activity tracking and status
- Channel membership validation

### Referrals Collection
- Referrer and referred user relationship
- Referral status and rewards
- Timestamp tracking
- Validation metadata

### Transactions Collection
- Complete transaction history
- Token transfers and rewards
- Admin actions and notes
- Audit trail for compliance

## 🔐 Security Features

- Input validation and sanitization
- Rate limiting for API calls
- Secure admin authentication
- Transaction integrity checks
- Comprehensive audit logging

## 📝 Logging

The bot maintains detailed logs in the `logs/` directory:
- `bot.log`: General bot operations
- `error.log`: Error tracking and debugging
- `security.log`: Security events and admin actions
- `transactions.log`: All token transactions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the logs in the `logs/` directory
- Review the configuration in `config.py`
- Ensure all environment variables are properly set
- Verify MongoDB connection and permissions

## 🔄 Updates and Maintenance

- Regular dependency updates
- Database backup recommendations
- Performance monitoring
- Security patch management

---

**Genesis Bot** - Empowering communities through referral rewards and token incentives! 🌟
