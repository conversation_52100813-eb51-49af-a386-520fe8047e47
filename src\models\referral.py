"""
Referral model for the Telegram Referral Bot
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
import logging

logger = logging.getLogger(__name__)

class ReferralStatus(Enum):
    """Referral status"""
    PENDING = "pending"
    COMPLETED = "completed"
    INVALID = "invalid"

@dataclass
class Referral:
    """Referral model representing user referrals"""
    
    referrer_id: int  # User who made the referral
    referred_id: int  # User who was referred
    referral_code: str
    
    # Status and reward
    status: ReferralStatus = ReferralStatus.PENDING
    reward_amount: float = 0.0
    is_rewarded: bool = False
    
    # Optional fields
    referral_id: Optional[str] = None
    transaction_id: Optional[str] = None  # Associated transaction ID
    
    # Timestamps
    created_at: datetime = None
    completed_at: Optional[datetime] = None
    rewarded_at: Optional[datetime] = None
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.referral_id is None:
            self.referral_id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.metadata is None:
            self.metadata = {}
    
    def complete_referral(self, reward_amount: float, transaction_id: Optional[str] = None):
        """Mark referral as completed and set reward"""
        self.status = ReferralStatus.COMPLETED
        self.reward_amount = reward_amount
        self.completed_at = datetime.now(timezone.utc)
        
        if transaction_id:
            self.transaction_id = transaction_id
    
    def mark_rewarded(self, transaction_id: Optional[str] = None):
        """Mark referral as rewarded"""
        self.is_rewarded = True
        self.rewarded_at = datetime.now(timezone.utc)
        
        if transaction_id:
            self.transaction_id = transaction_id
    
    def invalidate_referral(self, reason: str = ""):
        """Mark referral as invalid"""
        self.status = ReferralStatus.INVALID
        
        if reason:
            self.metadata['invalid_reason'] = reason
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to referral"""
        self.metadata[key] = value
    
    def get_status_display(self) -> str:
        """Get human-readable status"""
        status_map = {
            ReferralStatus.PENDING: "⏳ Pending",
            ReferralStatus.COMPLETED: "✅ Completed",
            ReferralStatus.INVALID: "❌ Invalid"
        }
        return status_map.get(self.status, str(self.status.value))
    
    def get_formatted_reward(self, currency_symbol: str = "💎") -> str:
        """Get formatted reward amount"""
        return f"{currency_symbol}{self.reward_amount:.2f}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert referral object to dictionary"""
        data = asdict(self)
        
        # Convert enum values to strings
        data['status'] = self.status.value
        
        # Convert datetime objects to ISO format
        datetime_fields = ['created_at', 'completed_at', 'rewarded_at']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Referral':
        """Create referral object from dictionary"""
        # Create a copy of data to avoid modifying the original
        referral_data = data.copy()

        # Remove MongoDB's _id field if present
        referral_data.pop('_id', None)

        # Remove any unknown fields that aren't part of the Referral model
        known_fields = {
            'referrer_id', 'referred_id', 'referral_code', 'status', 'reward_amount',
            'is_rewarded', 'referral_id', 'transaction_id', 'created_at',
            'completed_at', 'rewarded_at', 'metadata'
        }
        referral_data = {k: v for k, v in referral_data.items() if k in known_fields}

        # Convert string values back to enums
        if 'status' in referral_data:
            status_value = referral_data['status']
            # Handle both uppercase and lowercase status values
            if isinstance(status_value, str):
                status_value = status_value.lower()
            referral_data['status'] = ReferralStatus(status_value)

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'completed_at', 'rewarded_at']
        for field in datetime_fields:
            if field in referral_data and referral_data[field]:
                if isinstance(referral_data[field], str):
                    try:
                        # Handle various ISO format variations
                        date_str = referral_data[field]
                        # Replace 'Z' with '+00:00' for timezone handling
                        if date_str.endswith('Z'):
                            date_str = date_str[:-1] + '+00:00'
                        referral_data[field] = datetime.fromisoformat(date_str)
                    except (ValueError, AttributeError) as e:
                        # If conversion fails, set to None to avoid strftime errors
                        logger.warning(f"Failed to convert {field} datetime string '{referral_data[field]}': {e}")
                        referral_data[field] = None
                elif not isinstance(referral_data[field], datetime):
                    # If it's not a string or datetime, set to None
                    logger.warning(f"Unexpected type for {field}: {type(referral_data[field])}")
                    referral_data[field] = None

        return cls(**referral_data)
    
    def __str__(self) -> str:
        """String representation of referral"""
        return f"Referral(id={self.referral_id}, referrer={self.referrer_id}, referred={self.referred_id}, status={self.status.value})"
